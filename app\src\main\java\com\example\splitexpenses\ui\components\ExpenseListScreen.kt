package com.example.splitexpenses.ui.components


import androidx.compose.animation.*
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.example.splitexpenses.R
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.ui.components.ExportDialog
import com.example.splitexpenses.ui.components.ExpenseFilterMenu
import com.example.splitexpenses.ui.components.OfflineStatusIndicator
import com.example.splitexpenses.ui.viewmodels.ExpenseFilterState
import com.example.splitexpenses.ui.viewmodels.applyFilters
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.filled.Person
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import java.io.OutputStream
import androidx.compose.animation.slideIn
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOut
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.with
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.AlertDialog
import androidx.compose.ui.draw.clip
import androidx.compose.ui.modifier.modifierLocalConsumer
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.sp


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ExpenseListScreen(
    group: GroupData,
    onExpenseClick: (Expense) -> Unit,
    onShowBalanceDetailsClick: () -> Unit,
    onAddExpenseClick: () -> Unit,
    onDeleteExpense: (Set<String>) -> Unit,
    onShowStatisticsClick: () -> Unit,
    onShowManageMembersClick: () -> Unit,
    onShowDeleteGroupDialog: () -> Unit,
    onExportToCsv: (OutputStream) -> Boolean,
    isMultiSelectMode: Boolean,
    onMultiSelectModeChange: (Boolean) -> Unit,
    selectedExpenses: Set<String>,
    onSelectedExpensesChange: (String, Boolean) -> Unit,
    onNavigateToManageCategories: () -> Unit,
    isCurrentUserGroupCreator: Boolean = false,
    onEditGroupName: () -> Unit = {},
    isOffline: Boolean = false,
    filterState: ExpenseFilterState = ExpenseFilterState(),
    onFilterStateChange: (ExpenseFilterState) -> Unit = {}
) {
    var showExportDialog by remember { mutableStateOf(false) }
    var showDeleteConfirmationDialog by remember { mutableStateOf(false) }
    var showFilterMenu by remember { mutableStateOf(false) }

    // Handle back button specifically within this screen
    BackHandler(enabled = isMultiSelectMode || showFilterMenu) {
        when {
            showFilterMenu -> showFilterMenu = false
            isMultiSelectMode -> onMultiSelectModeChange(false)
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Group header row with menu
            var showGroupMenu by remember { mutableStateOf(false) }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = group.name,
                        style = MaterialTheme.typography.displayMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                val currentState = if (isMultiSelectMode) {
                    Icons.Default.Delete
                } else {
                    Icons.Default.MoreVert
                }

                Box(
                    contentAlignment = Alignment.Center
                ) {
                    // Animate icon changes with directional sliding
                    AnimatedContent(
                        targetState = currentState,
                        transitionSpec = {
                            val transition = if (isMultiSelectMode) {
                                slideInHorizontally(initialOffsetX = { fullWidth -> fullWidth })+ fadeIn() togetherWith
                                        slideOutHorizontally(targetOffsetX = { fullWidth -> fullWidth })+ fadeOut()
                            } else {
                                slideInHorizontally(initialOffsetX = { fullWidth -> fullWidth })+ fadeIn() togetherWith
                                        slideOutHorizontally(targetOffsetX = { fullWidth -> fullWidth })+ fadeOut()
                            }
                            transition.using(SizeTransform(clip = false))
                        },
                        label = "iconAnimation"
                    ) { icon ->
                        IconButton(
                            onClick = {
                                if (isMultiSelectMode) {
                                    if (!isOffline) {
                                        showDeleteConfirmationDialog = true
                                    }
                                } else {
                                    showGroupMenu = true
                                }
                            },
                            enabled = !isMultiSelectMode || !isOffline
                        ) {
                            Icon(
                                imageVector = icon,
                                contentDescription = null,
                                tint = if (isMultiSelectMode) {
                                    MaterialTheme.colorScheme.error
                                } else {
                                    MaterialTheme.colorScheme.onSurfaceVariant
                                }
                            )
                        }
                    }
                    DropdownMenu(
                        expanded = showGroupMenu,
                        onDismissRequest = { showGroupMenu = false },
                        containerColor = MaterialTheme.colorScheme.surface,
                        tonalElevation = 8.dp,
                        modifier = Modifier.animateContentSize(
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            )
                        ),
                    ) {
                        DropdownMenuItem(
                            text = { Text("Statistics") },
                            onClick = {
                                showGroupMenu = false
                                onShowStatisticsClick()
                            },
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.chart_arc),
                                    contentDescription = "Statistics"
                                )
                            }
                        )

                        DropdownMenuItem(
                            text = {
                                Text(
                                    "Manage Categories",
                                    color = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.onSurface
                                )
                            },
                            onClick = {
                                if (!isOffline) {
                                    showGroupMenu = false
                                    onNavigateToManageCategories()
                                }
                            },
                            enabled = !isOffline,
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.tag_outline),
                                    contentDescription = "Manage Categories",
                                    tint = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.onSurface
                                )
                            }
                        )

                        DropdownMenuItem(
                            text = {
                                Text(
                                    "Manage Group",
                                    color = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.onSurface
                                )
                            },
                            onClick = {
                                if (!isOffline) {
                                    showGroupMenu = false
                                    println("ExpenseListScreen: Clicked Manage Members")
                                    onShowManageMembersClick()
                                }
                            },
                            enabled = !isOffline,
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.account_outline),
                                    contentDescription = "Manage Members",
                                    tint = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.onSurface
                                )
                            }
                        )

                        DropdownMenuItem(
                            text = { Text("Export to CSV") },
                            onClick = {
                                showGroupMenu = false
                                showExportDialog = true
                            },
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.export),
                                    contentDescription = "Export"
                                )
                            }
                        )

                        DropdownMenuItem(
                            text = {
                                Text(
                                    "Delete Group",
                                    color = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.error
                                )
                            },
                            onClick = {
                                if (!isOffline) {
                                    showGroupMenu = false
                                    onShowDeleteGroupDialog()
                                }
                            },
                            enabled = !isOffline,
                            leadingIcon = {
                                Icon(
                                    Icons.Default.Delete,
                                    contentDescription = "Delete",
                                    tint = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.error
                                )
                            }
                        )
                    }
                }
            }

            // Show offline status indicator if offline
            if (isOffline) {
                OfflineStatusIndicator(
                    isOffline = true,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Balances summary with filter icon
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .clickable(onClick = onShowBalanceDetailsClick)
                ) {
                    // Apply filters to get filtered total
                    val filteredExpenses = group.expenses.applyFilters(filterState)
                    val totalSpent = filteredExpenses.sumOf { it.amount }

                    // Animate the total spent value
                    val animatedTotalSpent = animateFloatAsState(
                        targetValue = totalSpent.toFloat(),
                        animationSpec = tween(
                            durationMillis = 800,
                            easing = FastOutSlowInEasing
                        ),
                        label = "totalSpent"
                    )

                    Text(
                        text = "Total spent: ${String.format("%.2f", animatedTotalSpent.value)}€",
                        style = MaterialTheme.typography.headlineMedium
                    )

                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = if (filterState.hasActiveFilters()) {
                            "Tap for breakdown • Showing ${filteredExpenses.size} of ${group.expenses.size} expenses"
                        } else {
                            "Tap for breakdown"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.secondary
                    )
                }

                // Filter icon button with badge
                Box {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .combinedClickable(
                                onClick = { showFilterMenu = true },
                                onLongClick = {
                                    // Clear all filters on long press
                                    onFilterStateChange(ExpenseFilterState())
                                }
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = if (filterState.hasActiveFilters()) {
                                painterResource(id = R.drawable.filter)
                            } else {
                                painterResource(id = R.drawable.filter_outline)
                            },
                            contentDescription = "Filter expenses • Long press to clear filters",
                            tint = if (filterState.hasActiveFilters()) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )
                    }

                    // Circular badge showing filter count
                    if (filterState.hasActiveFilters()) {
                        Surface(
                            modifier = Modifier
                                .size(20.dp)
                                .align(Alignment.TopEnd)
                                .padding(top = 4.dp, end = 4.dp),
                            shape = androidx.compose.foundation.shape.CircleShape,
                            color = MaterialTheme.colorScheme.error
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "${filterState.getActiveFilterCount()}",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onError,
                                    fontSize = 10.sp
                                )
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Expenses list
            val dateFormat = remember { SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()) }

            // Apply filters and sort expenses by date (most recent first)
            val filteredExpenses = group.expenses.applyFilters(filterState)
            val sortedExpenses = filteredExpenses.sortedByDescending { it.date }

            // Group expenses by date
            val expensesByDate = sortedExpenses.groupBy { expense ->
                // Extract the date part only (without time) for grouping
                val calendar = Calendar.getInstance()
                calendar.timeInMillis = expense.date
                calendar.set(Calendar.HOUR_OF_DAY, 0)
                calendar.set(Calendar.MINUTE, 0)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                calendar.timeInMillis
            }

            // Convert to list and sort by date (most recent first)
            val groupedExpenses = expensesByDate.entries
                .sortedByDescending { it.key } // Sort by date timestamp (most recent first)
                .map { (dateTimestamp, expenses) ->
                    // Format the date for display
                    val formattedDate = dateFormat.format(Date(dateTimestamp))
                    formattedDate to expenses
                }

            LazyColumn {
                groupedExpenses.forEach { (date, expenses) ->
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                        ) {
                            Text(
                                text = date,
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.inverseSurface
                            )
                        }
                    }

                    val sortedDayExpenses = expenses.sortedByDescending { it.date }
                    itemsIndexed(sortedDayExpenses) { index, expense ->
                        // Animate the border color and width for selected expenses
                        val isSelected = expense.id in selectedExpenses
                        val borderWidth = animateFloatAsState(
                            targetValue = if (isSelected) 2f else 1f,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "borderWidth"
                        )
                        val borderColor = animateColorAsState(
                            targetValue = if (isSelected)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.secondaryContainer,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "borderColor"
                        )

                            Surface(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp)
    //                                .animateItemPlacement(
    //                                    animationSpec = tween(
    //                                        durationMillis = 300,
    //                                        easing = FastOutSlowInEasing
    //                                    )
    //                                )
                                    .combinedClickable(
                                        onClick = {
                                            if (isMultiSelectMode) {
                                                if (!isOffline) {
                                                    onSelectedExpensesChange(
                                                        expense.id,
                                                        expense.id !in selectedExpenses
                                                    )
                                                }
                                            } else {
                                                // Allow viewing expense details even when offline
                                                onExpenseClick(expense)
                                            }
                                        },
                                        onLongClick = {
                                            if (!isMultiSelectMode && !isOffline) {
                                                onMultiSelectModeChange(true)
                                                onSelectedExpensesChange(expense.id, true)
                                            }
                                        }
                                    ),
                                shape = MaterialTheme.shapes.medium,
                                color = MaterialTheme.colorScheme.surface,
                                border = BorderStroke(
                                    width = borderWidth.value.dp,
                                    color = borderColor.value
                                ),
                            ) {
                                ListItem(
                                    headlineContent = {
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Text(
                                                text = "${group.categories.find { it.name == expense.category }?.emoji ?: "💰"} ${expense.description}",
                                                style = MaterialTheme.typography.titleLarge
                                            )

                                            // Animate the expense amount
                                            val animatedAmount = animateFloatAsState(
                                                targetValue = expense.amount.toFloat(),
                                                animationSpec = tween(
                                                    durationMillis = 800,
                                                    easing = FastOutSlowInEasing
                                                ),
                                                label = "expenseAmount"
                                            )
                                            Text(
                                                text = "%.2f€".format(animatedAmount.value),
                                                style = MaterialTheme.typography.headlineSmall,
                                                color = MaterialTheme.colorScheme.primary
                                            )
                                        }
                                    },
                                    supportingContent = {
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Text(
                                                text = expense.category,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                fontStyle = FontStyle.Italic
                                            )
                                            Text(
                                                text = "Paid by ${expense.paidBy}",
                                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                fontStyle = FontStyle.Italic
                                            )
                                        }
                                    }
                                )
                            }

                    }
                }
                // Add padding at the bottom
                item {
                    Spacer(modifier = Modifier.height(80.dp))
                }
            }
        }


        // FAB for adding expenses
        Box(
            modifier = Modifier
                .padding(16.dp)
                .align(Alignment.BottomEnd)
        ) {
            FloatingActionButton(
                onClick = onAddExpenseClick
            ) {
                Icon(Icons.Default.Add, contentDescription = "Add Expense")
            }
        }

        // No category management dialog anymore - using screen navigation instead

        // Export dialog
        if (showExportDialog) {
            ExportDialog(
                groupName = group.name,
                onDismiss = { showExportDialog = false },
                onExport = onExportToCsv
            )
        }

        // Delete confirmation dialog
        if (showDeleteConfirmationDialog) {
            DeleteMultipleExpensesDialog(
                expenseCount = selectedExpenses.size,
                onDismiss = { showDeleteConfirmationDialog = false },
                onConfirm = {
                    onDeleteExpense(selectedExpenses)
                    onMultiSelectModeChange(false)
                    showDeleteConfirmationDialog = false
                }
            )
        }

        // Filter menu
        ExpenseFilterMenu(
            isVisible = showFilterMenu,
            filterState = filterState,
            availableCategories = group.categories,
            availableMembers = group.members,
            allExpenses = group.expenses,
            onFilterStateChange = onFilterStateChange,
            onDismiss = { showFilterMenu = false }
        )
    }
}
