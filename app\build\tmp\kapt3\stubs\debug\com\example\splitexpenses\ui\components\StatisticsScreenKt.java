package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001aK\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\u0014\b\u0002\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u00a2\u0006\u0002\u0010\u000b\u001a \u0010\f\u001a\u00020\u00012\b\u0010\r\u001a\u0004\u0018\u00010\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0010H\u0007\u00a8\u0006\u0011"}, d2 = {"PieChart", "", "data", "", "", "colors", "Landroidx/compose/ui/graphics/Color;", "selectedSectorIndex", "", "onSectorSelected", "Lkotlin/Function1;", "(Ljava/util/List;Ljava/util/List;Ljava/lang/Integer;Lkotlin/jvm/functions/Function1;)V", "StatisticsScreen", "group", "Lcom/example/splitexpenses/data/GroupData;", "onBackClick", "Lkotlin/Function0;", "app_debug"})
public final class StatisticsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class, androidx.compose.animation.ExperimentalAnimationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void StatisticsScreen(@org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick) {
    }
    
    /**
     * An interactive pie chart component with selection support.
     * Supports clicking on sectors to highlight them and provides visual feedback.
     */
    @androidx.compose.runtime.Composable()
    public static final void PieChart(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Double> data, @org.jetbrains.annotations.NotNull()
    java.util.List<androidx.compose.ui.graphics.Color> colors, @org.jetbrains.annotations.Nullable()
    java.lang.Integer selectedSectorIndex, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onSectorSelected) {
    }
}